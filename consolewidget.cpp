#include "consolewidget.h"
#include "admc_api_wrapper.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QScrollBar>
#include <QKeyEvent>
#include <QRegularExpression>
#include <QRegularExpressionMatch>
#include <QCompleter>
#include <QAbstractItemView>
#include <QSplitter>
#include <QDateTime>
#include <QLabel>
#include <QApplication>
#include <QFile>
#include <QTextStream>

ConsoleWidget::ConsoleWidget(QWidget *parent)
    : QWidget(parent), m_splitter(nullptr), m_logView(nullptr), m_commandView(nullptr), m_promptPosition(0), m_completer(nullptr)
{
    qRegisterMetaType<LogLevel>("LogLevel"); // 注册LogLevel，使其可以用于跨线程信号槽
    setupUi();
    connect(AdmcApiWrapper::getInstance(), &AdmcApiWrapper::logMessage, this, &ConsoleWidget::onApiLog);

    // 默认开启日志记录
    AdmcApiWrapper::getInstance()->registerLogCallback();
    // 设置默认日志级别
    LogLevel defaultLevel = static_cast<LogLevel>(m_logLevelComboBox->currentData().toInt());
    AdmcApiWrapper::getInstance()->SetLogLevel(defaultLevel);
    // 打印一条信息以确认
    onApiLog(LOG_INFO, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"), "Local", "日志系统已默认启动。");
}

ConsoleWidget::~ConsoleWidget()
{
}

void ConsoleWidget::setupUi()
{
    // 1. 创建新的日志视图
    m_logView = new QTextEdit(this);
    m_logView->document()->setMaximumBlockCount(1000); // 预设最多显示1000行
    m_logView->setReadOnly(true); // 日志窗口只读
    m_logView->setFont(QFont("Consolas", 10));
    m_logView->setStyleSheet("QTextEdit { background-color: #FFFFFF; color: #000000; border: none; }");

    // 2. 创建命令视图 (之前的 m_logDisplay)
    m_commandView = new QTextEdit(this);
    m_commandView->document()->setMaximumBlockCount(1000);
    m_commandView->setReadOnly(false);
    m_commandView->setFont(QFont("Consolas", 10));
    m_commandView->setStyleSheet("QTextEdit { background-color: #FFFFFF; color: #000000; border: none; }");
    m_commandView->installEventFilter(this);

    // 3. 创建并配置QSplitter
    m_splitter = new QSplitter(Qt::Vertical, this);
    m_splitter->addWidget(m_logView);
    m_splitter->setStretchFactor(0, 1); // 初始高度比例
    m_splitter->setStretchFactor(1, 1);

    // 4. 创建控制按钮和下拉框
    QHBoxLayout* controlLayout = new QHBoxLayout();
    m_clearLogButton = new QPushButton("清空日志", this);
    m_saveLogButton = new QPushButton("保存日志", this);
    m_logLevelComboBox = new QComboBox(this);
    m_logLevelComboBox->addItem("Debug", LOG_DEBUG);
    m_logLevelComboBox->addItem("Info", LOG_INFO);
    m_logLevelComboBox->addItem("Warning", LOG_WARNING);
    m_logLevelComboBox->addItem("Error", LOG_ERROR);
    m_logLevelComboBox->addItem("Fatal", LOG_FATAL);
    m_logLevelComboBox->setCurrentIndex(1); // 默认显示Info级别及以上

    controlLayout->addWidget(m_clearLogButton);
    controlLayout->addWidget(m_saveLogButton);
    controlLayout->addStretch();
    controlLayout->addWidget(new QLabel("日志级别:", this));
    controlLayout->addWidget(m_logLevelComboBox);

    // 5. 创建下方面板，容纳控制条和命令视图
    QWidget* bottomPane = new QWidget(this);
    QVBoxLayout* bottomLayout = new QVBoxLayout(bottomPane);
    bottomLayout->setContentsMargins(0, 0, 0, 0);
    bottomLayout->setSpacing(2);
    bottomLayout->addLayout(controlLayout);
    bottomLayout->addWidget(m_commandView);
    bottomPane->setLayout(bottomLayout);

    // 6. 将下方面板添加到splitter
    m_splitter->addWidget(bottomPane);

    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(m_splitter);
    setLayout(mainLayout);

    // 7. 连接信号槽
    connect(m_clearLogButton, &QPushButton::clicked, this, &ConsoleWidget::onClearLogClicked);
    connect(m_saveLogButton, &QPushButton::clicked, this, &ConsoleWidget::onSaveLogClicked);
    connect(m_logLevelComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ConsoleWidget::onLogLevelChanged);

    setupCompleter();
    insertPrompt();
}

void ConsoleWidget::setupCompleter()
{
    QStringList commandList;
    commandList << "crd1.getaxis[0].fbpos"
                << "crd1.getaxis[1].fbpos"
                << "crd1.getaxis[2].fbpos"
                << "crd1.getaxis[3].fbpos"
                << "axisOn(0)" << "axisOff(0)"
                << "axisOn(1)" << "axisOff(1)"
                << "axisOn(2)" << "axisOff(2)"
                << "axisOn(3)" << "axisOff(3)"
                << "getErrorString()";

    m_completer = new QCompleter(commandList, this);
    m_completer->setWidget(m_commandView);
    m_completer->setCompletionMode(QCompleter::PopupCompletion);
    m_completer->setCaseSensitivity(Qt::CaseInsensitive);
    connect(m_completer, QOverload<const QString &>::of(&QCompleter::activated),
            this, &ConsoleWidget::insertCompletion);
}

bool ConsoleWidget::eventFilter(QObject *object, QEvent *event)
{
    if (object != m_commandView || event->type() != QEvent::KeyPress) {
        return QWidget::eventFilter(object, event);
    }

    QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);

    if (m_completer && m_completer->popup()->isVisible()) {
        switch (keyEvent->key()) {
        case Qt::Key_Enter:
        case Qt::Key_Return:
            if (m_completer->currentRow() != -1) { // A more reliable check
                insertCompletion(m_completer->currentCompletion());
                m_completer->popup()->hide();
            } else {
                processCurrentCommand();
                insertPrompt();
            }
            return true; 

        case Qt::Key_Escape:
            m_completer->popup()->hide();
            return true;

        case Qt::Key_Up:
        case Qt::Key_Down:
        case Qt::Key_Tab:
        case Qt::Key_Backtab:
            return false; // Let the completer handle navigationnza

        default:
            break;
        }
    }

    if (keyEvent->key() == Qt::Key_Return || keyEvent->key() == Qt::Key_Enter) {
        processCurrentCommand();
        insertPrompt();
        return true;
    }
        
    bool isShortcut = ((keyEvent->modifiers() & Qt::ControlModifier) && keyEvent->key() == Qt::Key_C);
    if (m_commandView->textCursor().position() < m_promptPosition && !isShortcut) {
        m_commandView->moveCursor(QTextCursor::End);
    }
    
    QString currentText = textUnderCursor();
    if (!isShortcut && !currentText.isEmpty()) {
        m_completer->setCompletionPrefix(currentText);
        QRect cr = m_commandView->cursorRect();
        cr.setWidth(m_completer->popup()->sizeHintForColumn(0)
                    + m_completer->popup()->verticalScrollBar()->sizeHint().width());
        m_completer->complete(cr);
    } else {
        m_completer->popup()->hide();
    }
    
    return false;
}

void ConsoleWidget::onApiLog(LogLevel level, const QString& timestamp, const QString& ip, const QString& message)
{
    QColor color;
    QString levelStr;

    switch (level) {
        case LOG_DEBUG:   levelStr = "DEBUG";   color = Qt::darkGray;   break;
        case LOG_INFO:    levelStr = "INFO";    color = Qt::black;      break;
        case LOG_WARNING: levelStr = "WARN";    color = QColor("#D97800");  break; // Dark Orange
        case LOG_ERROR:   levelStr = "ERROR";   color = QColor("#C40000");  break; // Dark Red
        case LOG_FATAL:   levelStr = "FATAL";   color = QColor("#C40000");  break; // Dark Red
        default:          levelStr = "UNKN";    color = Qt::black;      break;
    }

    // 使用leftJustified填充空格，确保日志级别字段对齐（宽度为5，以DEBUG/ERROR为准）
    levelStr = levelStr.leftJustified(5, ' ');

    QString formattedMessage = QString("%1 [%2] [%3] %4")
                                   .arg(timestamp, levelStr, ip, message);

    // 智能滚动
    QScrollBar *scrollBar = m_logView->verticalScrollBar();
    bool atBottom = (scrollBar->value() >= scrollBar->maximum() - 5);

    m_logView->moveCursor(QTextCursor::End);
    m_logView->setTextColor(color);
    m_logView->insertPlainText(formattedMessage + "\n");

    if (atBottom) {
        m_logView->ensureCursorVisible(); // 等同于滚动到底部
    }
}

void ConsoleWidget::appendText(const QString& text, const QColor& color)
{
    m_commandView->setTextColor(color);
    m_commandView->insertPlainText(text);
    m_commandView->verticalScrollBar()->setValue(m_commandView->verticalScrollBar()->maximum());
}

void ConsoleWidget::insertPrompt()
{
    m_commandView->moveCursor(QTextCursor::End);
    m_commandView->setTextColor(QColor("#008000")); // Dark Green
    m_commandView->insertPlainText("\n> ");
    m_promptPosition = m_commandView->textCursor().position();
    m_commandView->verticalScrollBar()->setValue(m_commandView->verticalScrollBar()->maximum());
}

void ConsoleWidget::processCurrentCommand()
{
    QTextCursor cursor = m_commandView->textCursor();
    cursor.setPosition(m_promptPosition, QTextCursor::KeepAnchor);
    QString command = cursor.selectedText().trimmed();

    if (command.isEmpty()) {
        return;
    }
    
    processCommand(command);
}

void ConsoleWidget::processCommand(const QString& command)
{
    m_commandView->moveCursor(QTextCursor::End);

    QRegularExpression re("crd(\\d+)\\.getaxis\\[(\\d+)\\]\\.fbpos");
    QRegularExpressionMatch match = re.match(command);

    if (match.hasMatch()) {
        int axisId = match.captured(2).toInt();
        double position = 0.0;
        short ret = AdmcApiWrapper::getInstance()->getAxisPosition(axisId, position);
        if (ret == 0) {
            appendText(QString("\n  -> Axis %1 position: %2").arg(axisId).arg(position), QColor("#00008B")); // Dark Blue
        } else {
            appendText(QString("\n  -> Error: %1").arg(AdmcApiWrapper::getInstance()->getErrorString(ret)), QColor("#C40000")); // Dark Red
        }
    } else {
        re.setPattern("axis(On|Off)\\((\\d+)\\)");
        match = re.match(command);
        if (match.hasMatch()) {
            QString cmd = match.captured(1);
            int axisId = match.captured(2).toInt();
            short ret = (cmd == "On") ? AdmcApiWrapper::getInstance()->axisOn(axisId) : AdmcApiWrapper::getInstance()->axisOff(axisId);
            if (ret == 0) {
                appendText(QString("\n  -> Axis %1 %2 command executed.").arg(axisId).arg(cmd), QColor("#00008B")); // Dark Blue
            } else {
                appendText(QString("\n  -> Error: %1").arg(AdmcApiWrapper::getInstance()->getErrorString(ret)), QColor("#C40000")); // Dark Red
            }
        } else {
            // 检查getErrorString指令 - 支持十进制、十六进制和负值
            re.setPattern("getErrorString\\((-?(?:0x[0-9a-fA-F]+|\\d+))\\)");
            match = re.match(command);
            if (match.hasMatch()) {
                QString errorCodeStr = match.captured(1);
                bool ok;
                int errorCode;

                // 判断是否为十六进制格式
                if (errorCodeStr.startsWith("0x", Qt::CaseInsensitive)) {
                    errorCode = errorCodeStr.toInt(&ok, 16);
                } else {
                    errorCode = errorCodeStr.toInt(&ok, 10);
                }

                if (ok) {
                    QString errorString = AdmcApiWrapper::getInstance()->getErrorString(errorCode);
                    appendText(QString("\n  -> Error Code %1 (0x%2): %3")
                              .arg(errorCode)
                              .arg(QString::number(errorCode, 16).toUpper())
                              .arg(errorString), QColor("#00008B")); // Dark Blue
                } else {
                    appendText(QString("\n  -> Invalid error code format: %1").arg(errorCodeStr), QColor("#C40000")); // Dark Red
                }
            } else {
                appendText("\n  -> Unknown command.", QColor("#C40000")); // Dark Red
            }
        }
    }
}

void ConsoleWidget::insertCompletion(const QString &completion)
{
    QTextCursor tc = m_commandView->textCursor();
    QString text = textUnderCursor();
    tc.movePosition(QTextCursor::Left, QTextCursor::KeepAnchor, text.length());
    tc.insertText(completion);
    m_commandView->setTextCursor(tc);
}

QString ConsoleWidget::textUnderCursor() const
{
    QTextCursor tc = m_commandView->textCursor();
    tc.movePosition(QTextCursor::StartOfLine, QTextCursor::KeepAnchor);
    QString line = tc.selectedText();
    // Find last prompt
    int promptPos = line.lastIndexOf("> ");
    if (promptPos != -1) {
        return line.mid(promptPos + 2);
    }
    return QString();
}

void ConsoleWidget::onClearLogClicked()
{
    m_logView->clear();
}

void ConsoleWidget::onSaveLogClicked()
{
    QString logContent = m_logView->toPlainText();
    if (logContent.isEmpty()) {
        onApiLog(LOG_WARNING, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"), "Local", "日志内容为空，无需保存。");
        return;
    }

    QString dirPath = QApplication::applicationDirPath();
    QString fileName = QString("log_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss"));
    QString filePath = dirPath + "/" + fileName;

    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out << logContent;
        file.close();
        onApiLog(LOG_INFO, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"), "Local", QString("日志已成功保存到 %1").arg(filePath));
    } else {
        onApiLog(LOG_ERROR, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"), "Local", QString("无法保存日志文件到 %1").arg(filePath));
    }
}

void ConsoleWidget::onLogLevelChanged(int index)
{
    if (index < 0) return;
    LogLevel selectedLevel = static_cast<LogLevel>(m_logLevelComboBox->itemData(index).toInt());
    AdmcApiWrapper::getInstance()->SetLogLevel(selectedLevel);
    onApiLog(LOG_INFO, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"), "Local", QString("日志级别已设置为 %1。").arg(m_logLevelComboBox->currentText()));
} 